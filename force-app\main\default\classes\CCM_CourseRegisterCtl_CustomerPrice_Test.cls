/**
 * Test class for customer-specific pricing functionality in CCM_CourseRegisterCtl
 */
@isTest
public class CCM_CourseRegisterCtl_CustomerPrice_Test {
    
    @testSetup
    static void setupTestData() {
        // Create test Account
        Account testAccount = new Account(Name = 'Test Customer Account');
        insert testAccount;
        
        // Create test Course Product
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        
        // Create test Training Course Setting
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        // Create test Training PriceBook
        Traning_PriceBook__c testPriceBook = new Traning_PriceBook__c();
        testPriceBook.Name = 'Test Training PriceBook';
        testPriceBook.IsActive__c = true;
        insert testPriceBook;
        
        // Create test Training PriceBook Entry
        Traning_PriceBook_Entry__c testPriceEntry = new Traning_PriceBook_Entry__c();
        testPriceEntry.Traning_PriceBook__c = testPriceBook.Id;
        testPriceEntry.Course_Product__c = objProduct.Id;
        testPriceEntry.UnitPrice__c = 150.00;
        testPriceEntry.Start_Date__c = Date.today().addDays(-30);
        testPriceEntry.End_Date__c = Date.today().addDays(30);
        testPriceEntry.CurrencyIsoCode = 'EUR';
        insert testPriceEntry;
        
        // Create test Course Arrangement
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today(),
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = false,
            Traning_PriceBook__c = testPriceBook.Id,
            Price__c = 100.00  // Default price
        );
        insert courseArrangement;
        
        // Create Course Price Per Customer with different price
        Course_Price_Per_Customer__c coursePricePerCustomer = new Course_Price_Per_Customer__c();
        coursePricePerCustomer.Course_Arrangement__c = courseArrangement.Id;
        coursePricePerCustomer.Customer__c = testAccount.Id;
        coursePricePerCustomer.Training_Price_Book__c = testPriceBook.Id;
        coursePricePerCustomer.Price__c = 200.00;  // Customer-specific price
        coursePricePerCustomer.CurrencyIsoCode = 'EUR';
        insert coursePricePerCustomer;
    }
    
    @isTest
    static void testGetCustomerSpecificPrice_WithCustomerPrice() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        
        Test.startTest();
        
        // Test getting customer-specific price
        Decimal customerPrice = CCM_CourseRegisterCtl.getCustomerSpecificPrice(courseArrangement.Id, testAccount.Id);
        
        Test.stopTest();
        
        // Verify customer-specific price is returned
        System.assertEquals(200.00, customerPrice, 'Should return customer-specific price');
    }
    
    @isTest
    static void testGetCustomerSpecificPrice_WithoutCustomerPrice() {
        // Create another account without customer-specific pricing
        Account anotherAccount = new Account(Name = 'Another Customer Account');
        insert anotherAccount;
        
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        
        Test.startTest();
        
        // Test getting customer-specific price for customer without specific pricing
        Decimal customerPrice = CCM_CourseRegisterCtl.getCustomerSpecificPrice(courseArrangement.Id, anotherAccount.Id);
        
        Test.stopTest();
        
        // Verify null is returned when no customer-specific price exists
        System.assertEquals(null, customerPrice, 'Should return null when no customer-specific price exists');
    }
    
    @isTest
    static void testGetCustomerSpecificPrice_WithBlankParameters() {
        Test.startTest();
        
        // Test with blank course arrangement ID
        Decimal customerPrice1 = CCM_CourseRegisterCtl.getCustomerSpecificPrice('', 'someCustomerId');
        
        // Test with blank customer ID
        Decimal customerPrice2 = CCM_CourseRegisterCtl.getCustomerSpecificPrice('someCourseArrangementId', '');
        
        // Test with both blank
        Decimal customerPrice3 = CCM_CourseRegisterCtl.getCustomerSpecificPrice('', '');
        
        Test.stopTest();
        
        // Verify null is returned for all blank parameter scenarios
        System.assertEquals(null, customerPrice1, 'Should return null for blank course arrangement ID');
        System.assertEquals(null, customerPrice2, 'Should return null for blank customer ID');
        System.assertEquals(null, customerPrice3, 'Should return null for both blank parameters');
    }

    @isTest
    static void testQueryCourseArrangementListWithCustomerPrice() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        Training_Course_Setting__c courseSetting = [SELECT Id FROM Training_Course_Setting__c LIMIT 1];

        Test.startTest();

        // Test queryCourseArrangementList method
        Map<String, Object> result = CCM_CourseRegisterCtl.queryCourseArrangementList(courseSetting.Id);

        Test.stopTest();

        // Verify the result contains arrangement info
        System.assertNotEquals(null, result, 'Result should not be null');
        List<Object> arrangementList = (List<Object>)result.get('lstArrangemtInfo');
        System.assertNotEquals(null, arrangementList, 'Arrangement list should not be null');

        if(arrangementList != null && arrangementList.size() > 0) {
            Map<String, Object> arrangementInfo = (Map<String, Object>)arrangementList[0];
            // The price should be customer-specific price (200.00) if customer context is available
            // or default price (100.00) if no customer context
            Object priceObj = arrangementInfo.get('arrangementprice');
            System.assertNotEquals(null, priceObj, 'Price should not be null');
        }
    }

    @isTest
    static void testQueryCourseArrangementInfoWithCustomerPrice() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];

        Test.startTest();

        // Test queryCourseArrangementInfo method
        Map<String, Object> result = CCM_CourseRegisterCtl.queryCourseArrangementInfo(courseArrangement.Id, testAccount.Id);

        Test.stopTest();

        // Verify the result contains customer-specific price
        System.assertNotEquals(null, result, 'Result should not be null');
        Object priceObj = result.get('price');
        System.assertNotEquals(null, priceObj, 'Price should not be null');

        // The price should be customer-specific price (200.00)
        System.assertEquals(200.00, (Decimal)priceObj, 'Should return customer-specific price');
    }

    @isTest
    static void testCCMCourseRegisterPreviewCtlWithCustomerPrice() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];

        // Create Course Register
        Course_Register__c courseRegister = new Course_Register__c();
        courseRegister.Customer__c = testAccount.Id;
        courseRegister.Course_Arrangement__c = courseArrangement.Id;
        courseRegister.Status__c = 'Draft';
        courseRegister.PCS__c = 1;
        courseRegister.Price_Pcs__c = 100.00;
        insert courseRegister;

        // Create Course Register Item
        Course_Register_Item__c registerItem = new Course_Register_Item__c();
        registerItem.Course_Register__c = courseRegister.Id;
        registerItem.Course_Arrangement__c = courseArrangement.Id;
        registerItem.Trainee__c = 'Test Trainee';
        insert registerItem;

        Test.startTest();

        // Test CCM_CourseRegisterPreviewCtl.queryRegisterInfo method
        Map<String, Object> result = CCM_CourseRegisterPreviewCtl.queryRegisterInfo(courseRegister.Id);

        Test.stopTest();

        // Verify the result contains customer-specific price
        System.assertNotEquals(null, result, 'Result should not be null');
        List<Object> mapItemInfo = (List<Object>)result.get('mapItemInfo');
        System.assertNotEquals(null, mapItemInfo, 'mapItemInfo should not be null');

        if(mapItemInfo != null && mapItemInfo.size() > 0) {
            Map<String, Object> itemInfo = (Map<String, Object>)mapItemInfo[0];
            Object listPriceObj = itemInfo.get('listPrice');
            System.assertNotEquals(null, listPriceObj, 'List price should not be null');

            // The price should be customer-specific price (200.00)
            System.assertEquals(200.00, (Decimal)listPriceObj, 'Should return customer-specific price in item list');
        }
    }
}
