/**
 * Test class for CCM_CoursePricePerCustomerHandler
 */
@isTest
public class CCM_CoursePricePerCustomerHandler_Test {
    
    @testSetup
    static void setupTestData() {
        // Create test Account
        Account testAccount = new Account(Name = 'Test Customer Account');
        insert testAccount;
        
        // Create test Course Product
        Course_Product__c objProduct = new Course_Product__c();
        objProduct.Order_Model__c = 'E1204T';
        insert objProduct;
        
        // Create test Training Course Setting
        Training_Course_Setting__c objSetting = new Training_Course_Setting__c();
        objSetting.Course_Name__c = objProduct.Id;
        insert objSetting;
        
        // Create test Training PriceBook
        Traning_PriceBook__c testPriceBook = new Traning_PriceBook__c();
        testPriceBook.Name = 'Test Training PriceBook';
        testPriceBook.IsActive__c = true;
        insert testPriceBook;
        
        // Create test Training PriceBook Entry - using Course_Date__c for pricing validation
        Traning_PriceBook_Entry__c testPriceEntry = new Traning_PriceBook_Entry__c();
        testPriceEntry.Traning_PriceBook__c = testPriceBook.Id;
        testPriceEntry.Course_Product__c = objProduct.Id;
        testPriceEntry.UnitPrice__c = 150.00;
        testPriceEntry.Start_Date__c = Date.today().addDays(-30);  // Valid from 30 days ago
        testPriceEntry.End_Date__c = Date.today().addDays(90);    // Valid until 90 days from now (covers Course_Date__c)
        testPriceEntry.CurrencyIsoCode = 'EUR';
        insert testPriceEntry;
        
        // Create test Course Arrangement - Course_Date__c is different from today to test pricingDate logic
        Course_Arrangement__c courseArrangement = new Course_Arrangement__c(
            Training_Course_Setting__c = objSetting.Id,
            Course_Date__c = Date.today().addDays(60),  // Future date, different from pricingDate (today)
            Start_Time__c = Time.newInstance(9, 0, 0, 0),
            End_Time__c = Time.newInstance(10, 0, 0, 0),
            Total_Slot__c = 500,
            CurrencyIsoCode = 'EUR',
            Free_Training__c = false
        );
        insert courseArrangement;
    }
    
    @isTest
    static void testInsertCoursePricePerCustomer() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        Traning_PriceBook__c testPriceBook = [SELECT Id FROM Traning_PriceBook__c LIMIT 1];
        
        Test.startTest();
        
        // Create Course Price Per Customer
        Course_Price_Per_Customer__c coursePricePerCustomer = new Course_Price_Per_Customer__c();
        coursePricePerCustomer.Course_Arrangement__c = courseArrangement.Id;
        coursePricePerCustomer.Customer__c = testAccount.Id;
        coursePricePerCustomer.Training_Price_Book__c = testPriceBook.Id;
        coursePricePerCustomer.CurrencyIsoCode = 'EUR';
        
        insert coursePricePerCustomer;
        
        Test.stopTest();
        
        // Verify price was calculated
        Course_Price_Per_Customer__c insertedRecord = [
            SELECT Id, Price__c 
            FROM Course_Price_Per_Customer__c 
            WHERE Id = :coursePricePerCustomer.Id
        ];
        
        System.assertEquals(150.00, insertedRecord.Price__c, 'Price should be calculated correctly');
    }
    
    @isTest
    static void testUpdateCoursePricePerCustomer() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        Traning_PriceBook__c testPriceBook = [SELECT Id FROM Traning_PriceBook__c LIMIT 1];
        
        // Create Course Price Per Customer without price book first
        Course_Price_Per_Customer__c coursePricePerCustomer = new Course_Price_Per_Customer__c();
        coursePricePerCustomer.Course_Arrangement__c = courseArrangement.Id;
        coursePricePerCustomer.Customer__c = testAccount.Id;
        coursePricePerCustomer.CurrencyIsoCode = 'EUR';
        insert coursePricePerCustomer;
        
        Test.startTest();
        
        // Update with price book
        coursePricePerCustomer.Training_Price_Book__c = testPriceBook.Id;
        update coursePricePerCustomer;
        
        Test.stopTest();
        
        // Verify price was calculated
        Course_Price_Per_Customer__c updatedRecord = [
            SELECT Id, Price__c 
            FROM Course_Price_Per_Customer__c 
            WHERE Id = :coursePricePerCustomer.Id
        ];
        
        System.assertEquals(150.00, updatedRecord.Price__c, 'Price should be calculated correctly after update');
    }
    
    @isTest
    static void testCoursePricePerCustomerWithoutValidPrice() {
        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id FROM Course_Arrangement__c LIMIT 1];
        
        // Create a price book without valid entries
        Traning_PriceBook__c testPriceBook = new Traning_PriceBook__c();
        testPriceBook.Name = 'Test Training PriceBook No Entries';
        testPriceBook.IsActive__c = true;
        insert testPriceBook;
        
        Test.startTest();
        
        try {
            // Create Course Price Per Customer with price book that has no valid entries
            Course_Price_Per_Customer__c coursePricePerCustomer = new Course_Price_Per_Customer__c();
            coursePricePerCustomer.Course_Arrangement__c = courseArrangement.Id;
            coursePricePerCustomer.Customer__c = testAccount.Id;
            coursePricePerCustomer.Training_Price_Book__c = testPriceBook.Id;
            coursePricePerCustomer.CurrencyIsoCode = 'EUR';
            
            insert coursePricePerCustomer;
            
            System.assert(false, 'Should have thrown an error for missing price entry');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Service_Training_No_Product') || 
                         e.getMessage().contains('FIELD_CUSTOM_VALIDATION_EXCEPTION'), 
                         'Should throw validation error for missing price entry');
        }
        
        Test.stopTest();
    }

    @isTest
    static void testPricingDateLogic() {
        // This test verifies that pricing uses Course_Date__c as pricingDate

        // Get test data
        Account testAccount = [SELECT Id FROM Account WHERE Name = 'Test Customer Account' LIMIT 1];
        Course_Arrangement__c courseArrangement = [SELECT Id, Course_Date__c FROM Course_Arrangement__c LIMIT 1];

        // Create a price book with entry that is valid on Course_Date__c but not valid today
        Traning_PriceBook__c testPriceBook = new Traning_PriceBook__c();
        testPriceBook.Name = 'Test PriceBook for Pricing Date Logic';
        testPriceBook.IsActive__c = true;
        insert testPriceBook;

        Course_Product__c objProduct = [SELECT Id FROM Course_Product__c LIMIT 1];

        // Create price entry that is valid on Course_Date__c (60 days from now) but not valid today
        Traning_PriceBook_Entry__c testPriceEntry = new Traning_PriceBook_Entry__c();
        testPriceEntry.Traning_PriceBook__c = testPriceBook.Id;
        testPriceEntry.Course_Product__c = objProduct.Id;
        testPriceEntry.UnitPrice__c = 250.00;
        testPriceEntry.Start_Date__c = Date.today().addDays(50);  // Valid from 50 days from now
        testPriceEntry.End_Date__c = Date.today().addDays(70);   // Valid until 70 days from now (covers Course_Date__c which is 60 days from now)
        testPriceEntry.CurrencyIsoCode = 'EUR';
        insert testPriceEntry;

        Test.startTest();

        // Create Course Price Per Customer
        Course_Price_Per_Customer__c coursePricePerCustomer = new Course_Price_Per_Customer__c();
        coursePricePerCustomer.Course_Arrangement__c = courseArrangement.Id;
        coursePricePerCustomer.Customer__c = testAccount.Id;
        coursePricePerCustomer.Training_Price_Book__c = testPriceBook.Id;
        coursePricePerCustomer.CurrencyIsoCode = 'EUR';

        insert coursePricePerCustomer;

        Test.stopTest();

        // Verify price was calculated using Course_Date__c as pricingDate
        Course_Price_Per_Customer__c insertedRecord = [
            SELECT Id, Price__c
            FROM Course_Price_Per_Customer__c
            WHERE Id = :coursePricePerCustomer.Id
        ];

        // If the logic uses Course_Date__c (60 days from now), the price should be calculated as 250.00
        // If it incorrectly uses today's date, it would fail because the price entry is not valid today
        System.assertEquals(250.00, insertedRecord.Price__c, 'Price should be calculated using Course_Date__c as pricingDate');
    }
}
