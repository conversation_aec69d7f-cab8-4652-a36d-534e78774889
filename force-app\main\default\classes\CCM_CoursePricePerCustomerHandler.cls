/**
 * Author : AI Assistant
 * Description : Handler for Course Price Per Customer price calculation
 */
public without sharing class CCM_CoursePricePerCustomerHandler implements Triggers.Handler{
    static boolean isRun = true;
    
    public void handle(){
        system.debug('进入Course Price Per Customer handler');
        List<Course_Price_Per_Customer__c> lstCoursePricePerCustomer = new List<Course_Price_Per_Customer__c>();
        List<String> lstPriceBookIDs = new List<String>();
        List<String> lstCourseSettingIds = new List<String>();
        Set<String> setCourseArrangementIds = new Set<String>();

        if(isRun){
            if(Trigger.isInsert){
                for(Course_Price_Per_Customer__c objCoursePricePerCustomer : (List<Course_Price_Per_Customer__c>)Trigger.new){
                    if(objCoursePricePerCustomer.Training_Price_Book__c != null){
                        lstCoursePricePerCustomer.add(objCoursePricePerCustomer);
                        lstPriceBookIDs.add(objCoursePricePerCustomer.Training_Price_Book__c);
                        setCourseArrangementIds.add(objCoursePricePerCustomer.Course_Arrangement__c);
                    }
                }
            }

            if(Trigger.IsUpdate){
                Map<Id,Object> mapId2OldRecord =  (Map<Id, Object>)Trigger.oldMap;
                for(Course_Price_Per_Customer__c objCoursePricePerCustomer : (List<Course_Price_Per_Customer__c>)Trigger.new){
                    Course_Price_Per_Customer__c objOld = (Course_Price_Per_Customer__c)mapId2OldRecord.get(objCoursePricePerCustomer.Id);

                    if(objCoursePricePerCustomer.Training_Price_Book__c != objOld.Training_Price_Book__c){
                        if(objCoursePricePerCustomer.Training_Price_Book__c != null){
                            lstCoursePricePerCustomer.add(objCoursePricePerCustomer);
                            lstPriceBookIDs.add(objCoursePricePerCustomer.Training_Price_Book__c);
                            setCourseArrangementIds.add(objCoursePricePerCustomer.Course_Arrangement__c);
                        }
                    }
                }
            }

            if(lstCoursePricePerCustomer.size() > 0){
                system.debug('计算Course Price Per Customer价格');
                calculatePricePerCustomer(lstCoursePricePerCustomer, lstPriceBookIDs, setCourseArrangementIds);
            }
        }
    }
    
    public void calculatePricePerCustomer(List<Course_Price_Per_Customer__c> lstCoursePricePerCustomer,
                                         List<String> lstPriceBookIDs,
                                         Set<String> setCourseArrangementIds){

        // 获取Course Arrangement的Course_Date__c和Training Course Setting信息
        List<Course_Arrangement__c> lstCourseArrangements = [
            SELECT Id, Course_Date__c, Training_Course_Setting__c
            FROM Course_Arrangement__c
            WHERE Id IN :setCourseArrangementIds
        ];

        // 创建Course Arrangement ID到Course Date的映射
        Map<String, Date> mapArrangementId2CourseDate = new Map<String, Date>();
        for(Course_Arrangement__c arrangement : lstCourseArrangements){
            mapArrangementId2CourseDate.put(arrangement.Id, arrangement.Course_Date__c);
        }

        // 根据PriceBookId查询Price Book Entry，不在这里过滤日期，在后面的循环中使用每个Course的Course_Date__c
        List<Traning_PriceBook_Entry__c> lstPriceEntry = [
            SELECT Id, UnitPrice__c, End_Date__c, Start_Date__c, Traning_PriceBook__c,
                   Traning_PriceBook__r.IsActive__c, Course_Product__c
            FROM Traning_PriceBook_Entry__c
            WHERE Traning_PriceBook__r.IsActive__c = TRUE
            AND Traning_PriceBook__c IN :lstPriceBookIDs
        ];
        
        if(lstPriceEntry == null || lstPriceEntry.size() == 0){
            lstCoursePricePerCustomer[0].addError(Label.Service_Training_No_Product);
            return;
        }
        
        // 创建PriceBook和Product到价格的映射
        Map<String, Map<String, Decimal>> mapPriceBook2ProductPrice = new Map<String, Map<String, Decimal>>();
        for(Traning_PriceBook_Entry__c objEntry : lstPriceEntry){
            if(!mapPriceBook2ProductPrice.containsKey(objEntry.Traning_PriceBook__c)){
                mapPriceBook2ProductPrice.put(objEntry.Traning_PriceBook__c, new Map<String, Decimal>());
            }
            mapPriceBook2ProductPrice.get(objEntry.Traning_PriceBook__c).put(objEntry.Course_Product__c, objEntry.UnitPrice__c);
        }
        
        // 获取Course Setting信息
        Set<String> setCourseSettingIds = new Set<String>();
        for(Course_Arrangement__c arrangement : lstCourseArrangements){
            setCourseSettingIds.add(arrangement.Training_Course_Setting__c);
        }
        
        List<Training_Course_Setting__c> lstTraining = [
            SELECT Id, Course_Name__c 
            FROM Training_Course_Setting__c 
            WHERE Id IN :setCourseSettingIds
        ];
        
        Map<String, String> mapArrangementId2ProductId = new Map<String, String>();
        for(Course_Arrangement__c arrangement : lstCourseArrangements){
            for(Training_Course_Setting__c courseSetting : lstTraining){
                if(courseSetting.Id == arrangement.Training_Course_Setting__c){
                    mapArrangementId2ProductId.put(arrangement.Id, courseSetting.Course_Name__c);
                    break;
                }
            }
        }
        
        // 计算每个Course Price Per Customer的价格
        for(Course_Price_Per_Customer__c objCoursePricePerCustomer : lstCoursePricePerCustomer){
            String productId = mapArrangementId2ProductId.get(objCoursePricePerCustomer.Course_Arrangement__c);
            Date courseDate = mapArrangementId2CourseDate.get(objCoursePricePerCustomer.Course_Arrangement__c);

            if(String.isNotBlank(productId) && objCoursePricePerCustomer.Training_Price_Book__c != null && courseDate != null){
                // 查找匹配的Price Book Entry，使用Course_Date__c作为pricingDate过滤有效期
                boolean priceFound = false;
                for(Traning_PriceBook_Entry__c entry : lstPriceEntry){
                    if(entry.Traning_PriceBook__c == objCoursePricePerCustomer.Training_Price_Book__c
                       && entry.Course_Product__c == productId
                       && entry.Start_Date__c <= courseDate
                       && entry.End_Date__c >= courseDate){
                        objCoursePricePerCustomer.Price__c = entry.UnitPrice__c;
                        priceFound = true;
                        break;
                    }
                }

                if(!priceFound){
                    objCoursePricePerCustomer.addError(Label.Service_Training_No_Product);
                }
            } else {
                objCoursePricePerCustomer.addError(Label.Service_Training_No_Product);
            }
        }
    }
    
    public CCM_CoursePricePerCustomerHandler() {
        
    }
}
