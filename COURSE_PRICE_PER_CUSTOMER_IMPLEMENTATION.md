# Course Price Per Customer Implementation

## 概述
为了支持Training上的多价格册逻辑，我们在Course Arrangement对象下添加了子对象Course Price Per Customer，使training支持不同的Customer使用不同的Price。

## 实现的功能

### 1. 新建对象：Course Price Per Customer
- **对象API名称**: `Course_Price_Per_Customer__c`
- **标签**: Course Price Per Customer
- **关系**: Course Arrangement的子对象（Master-Detail关系）

### 2. 字段定义
| 字段名称 | 字段类型 | 描述 |
|---------|---------|------|
| Course_Arrangement__c | Master-Detail | 关联到Course Arrangement对象 |
| Customer__c | Lookup | 关联到Account对象 |
| Training_Price_Book__c | Lookup | 关联到Training Pricebook对象 |
| Price__c | Currency(18,2) | 价格字段，保留2位小数 |

### 3. 价格计算逻辑
- **Course Price Per Customer的Price计算逻辑**：与Course Arrangement上的Price计算逻辑完全一致
- **定价日期逻辑**：使用Course_Arrangement__c上的Course_Date__c来判断选择哪个Training PriceBook Entry
- **Handler类**: `CCM_CoursePricePerCustomerHandler`
- **触发器**: `CoursePricePerCustomerTrigger`

### 4. 业务逻辑修改
- **CCM_CourseRegisterCtl类**：
  - 添加了`getCustomerSpecificPrice`方法来获取Customer特定的价格
  - 修改了`upsertRegisterCourse`和`upsertRegisterCourseArrangement`方法，优先使用Customer特定的价格
  - 如果没有Customer特定的价格，则使用Course Arrangement的默认价格

### 5. Layout配置
- **Course Price Per Customer Layout**: 显示Customer、Training Price Book、Price三个字段
- **Course Arrangement Layout**: 添加了Course Price Per Customer的相关列表

## 价格获取优先级
1. 首先查找Course Price Per Customer中是否有该Customer的特定价格
2. 如果没有找到，则使用Course Arrangement的默认价格

## 定价日期逻辑说明
Course Price Per Customer的价格计算使用Course_Arrangement__c上的Course_Date__c来判断Training PriceBook Entry的有效性，与Course Arrangement的价格计算逻辑完全一致。

### 逻辑一致性：
- **Course Arrangement价格计算**：使用`Date.valueOf(objCourse.Course_Date__c)`作为定价日期
- **Course Price Per Customer价格计算**：使用Course_Arrangement__c上的Course_Date__c作为定价日期

### 实现细节：
1. 在`CCM_CoursePricePerCustomerHandler.calculatePricePerCustomer()`方法中，从Course_Arrangement__c获取Course_Date__c
2. 创建Course Arrangement ID到Course Date的映射：`mapArrangementId2CourseDate`
3. 在价格计算循环中，使用Course_Date__c过滤Training PriceBook Entry的有效期：
   ```apex
   if(entry.Traning_PriceBook__c == objCoursePricePerCustomer.Training_Price_Book__c
      && entry.Course_Product__c == productId
      && entry.Start_Date__c <= courseDate
      && entry.End_Date__c >= courseDate)
   ```

### 业务意义：
- 确保Course Price Per Customer和Course Arrangement使用相同的定价逻辑
- 价格计算基于课程的实际日期，保证价格的时效性
- 提供一致的用户体验和业务逻辑

## 测试覆盖
- `CCM_CoursePricePerCustomerHandler_Test`: 测试价格计算Handler
- `CCM_CourseRegisterCtl_CustomerPrice_Test`: 测试Customer特定价格获取逻辑

## 文件清单

### 新建文件
1. `force-app/main/default/objects/Course_Price_Per_Customer__c/Course_Price_Per_Customer__c.object-meta.xml`
2. `force-app/main/default/objects/Course_Price_Per_Customer__c/fields/Course_Arrangement__c.field-meta.xml`
3. `force-app/main/default/objects/Course_Price_Per_Customer__c/fields/Customer__c.field-meta.xml`
4. `force-app/main/default/objects/Course_Price_Per_Customer__c/fields/Training_Price_Book__c.field-meta.xml`
5. `force-app/main/default/objects/Course_Price_Per_Customer__c/fields/Price__c.field-meta.xml`
6. `force-app/main/default/layouts/Course_Price_Per_Customer__c-Course Price Per Customer Layout.layout-meta.xml`
7. `force-app/main/default/classes/CCM_CoursePricePerCustomerHandler.cls`
8. `force-app/main/default/classes/CCM_CoursePricePerCustomerHandler.cls-meta.xml`
9. `force-app/main/default/classes/CCM_CoursePricePerCustomerHandler_Test.cls`
10. `force-app/main/default/classes/CCM_CoursePricePerCustomerHandler_Test.cls-meta.xml`
11. `force-app/main/default/classes/CCM_CourseRegisterCtl_CustomerPrice_Test.cls`
12. `force-app/main/default/classes/CCM_CourseRegisterCtl_CustomerPrice_Test.cls-meta.xml`
13. `force-app/main/default/triggers/CoursePricePerCustomerTrigger.trigger`
14. `force-app/main/default/triggers/CoursePricePerCustomerTrigger.trigger-meta.xml`

### 修改文件
1. `force-app/main/default/classes/CCM_CourseRegisterCtl.cls` - 添加了Customer特定价格获取逻辑
2. `force-app/main/default/layouts/Course_Arrangement__c-Course Arrangement Layout.layout-meta.xml` - 添加了Course Price Per Customer相关列表

## 使用说明
1. 在Course Arrangement记录页面，可以通过"Course Price Per Customer"相关列表添加Customer特定的价格
2. 为每个Customer配置不同的Training Price Book和Price
3. 当Customer注册Training时，系统会自动使用该Customer的特定价格（如果存在）
4. 如果没有配置Customer特定价格，系统会使用Course Arrangement的默认价格

## 注意事项
- Course Price Per Customer的价格计算依赖于Training Price Book Entry中的有效价格
- 价格计算会考虑Course Date来确保使用有效期内的价格
- 系统会自动处理价格计算，无需手动输入Price字段（除非需要覆盖计算结果）
