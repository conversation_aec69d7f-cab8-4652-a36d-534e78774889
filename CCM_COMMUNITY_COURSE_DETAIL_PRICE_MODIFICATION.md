# CCM_Community_Course_Detail 价格显示逻辑修改

## 修改概述
为了支持Course Price Per Customer的多价格册逻辑，修改了CCM_Community_Course_Detail页面相关的价格显示逻辑，使其能够显示Customer特定的价格。

## 修改的文件和方法

### 1. CCM_CourseRegisterCtl.cls

#### 修改的方法：

##### 1.1 queryCourseArrangementList方法
- **功能**: 查询Course Arrangement列表，在Course Detail页面第一步显示
- **修改内容**: 
  - 添加了获取当前用户Customer ID的逻辑
  - 在设置`arrangementprice`时，优先使用Customer特定价格
  - 如果没有Customer特定价格，则使用默认的Course Arrangement价格

```apex
// 修改前
mapArrangementInfo.put('arrangementprice', objCourse.Price__c);

// 修改后
// Get customer-specific price if available, otherwise use default arrangement price
Decimal customerPrice = getCustomerSpecificPrice(objCourse.Id, customerId);
Decimal finalPrice = customerPrice != null ? customerPrice : objCourse.Price__c;
mapArrangementInfo.put('arrangementprice', finalPrice);
```

##### 1.2 queryCourseArrangementInfo方法
- **功能**: 查询Course Arrangement详细信息，在Course Detail页面第二步显示
- **修改内容**:
  - 在设置`price`字段时，优先使用Customer特定价格
  - 如果没有Customer特定价格，则使用默认的Course Arrangement价格

```apex
// 修改前
mapFeild2Value.put('price', objCourse.Price__c == null ? 0 : objCourse.Price__c);

// 修改后
// Get customer-specific price if available, otherwise use default arrangement price
Decimal customerPrice = getCustomerSpecificPrice(objCourse.Id, customerId);
Decimal finalPrice = customerPrice != null ? customerPrice : (objCourse.Price__c == null ? 0 : objCourse.Price__c);
mapFeild2Value.put('price', finalPrice);
```

##### 1.3 新增getCurrentUserCustomerId方法
- **功能**: 获取当前用户的Customer ID
- **实现**: 调用CCM_PurchaseOrderDetailController.GetCurrentUserCustomer()方法

```apex
public static String getCurrentUserCustomerId() {
    try {
        Map<String,String> mapCustomerInfo = CCM_PurchaseOrderDetailController.GetCurrentUserCustomer();
        if(mapCustomerInfo != null) {
            return mapCustomerInfo.get('customerId');
        }
    } catch (Exception e) {
        System.debug('Error getting current user customer ID: ' + e.getMessage());
    }
    return null;
}
```

### 2. 测试类更新

#### 2.1 CCM_CourseRegisterCtl_CustomerPrice_Test.cls
- 添加了`testQueryCourseArrangementListWithCustomerPrice`测试方法
- 添加了`testQueryCourseArrangementInfoWithCustomerPrice`测试方法
- 验证了Customer特定价格在页面显示逻辑中的正确性

## 业务逻辑流程

### 价格显示优先级
1. **第一优先级**: Course Price Per Customer中的Customer特定价格
2. **第二优先级**: Course Arrangement的默认价格

### 页面显示逻辑
1. **Course Arrangement列表页面**:
   - 调用`queryCourseArrangementList`方法
   - 自动获取当前用户的Customer ID
   - 为每个Course Arrangement显示对应的Customer特定价格（如果存在）

2. **Course Arrangement详情页面**:
   - 调用`queryCourseArrangementInfo`方法
   - 传入Customer ID参数
   - 显示Customer特定价格（如果存在）

## 技术实现细节

### 价格获取逻辑
```apex
// 统一的价格获取逻辑
Decimal customerPrice = getCustomerSpecificPrice(courseArrangementId, customerId);
Decimal finalPrice = customerPrice != null ? customerPrice : defaultPrice;
```

### Customer ID获取
- 在`queryCourseArrangementList`中：自动获取当前用户的Customer ID
- 在`queryCourseArrangementInfo`中：使用传入的Customer ID参数

### 错误处理
- 所有价格获取操作都包含try-catch错误处理
- 当获取Customer特定价格失败时，自动回退到默认价格
- 记录详细的调试信息用于问题排查

## 影响的页面组件

### CCM_Community_Course_Detail组件
- **第一步**: Course Arrangement列表显示Customer特定价格
- **第二步**: Course Arrangement详情显示Customer特定价格
- **价格计算**: VAT计算基于Customer特定价格

### 前端显示
- 价格显示格式保持不变，使用`lightning:formattedNumber`组件
- 货币代码从Course Arrangement的CurrencyIsoCode获取
- 支持多货币显示

## 测试覆盖

### 单元测试
- 测试Customer特定价格的获取逻辑
- 测试默认价格的回退逻辑
- 测试空值和异常情况的处理

### 集成测试
- 测试页面方法中的价格显示逻辑
- 验证Customer特定价格在实际页面中的显示效果

## 部署注意事项

1. **数据依赖**: 确保Course Price Per Customer记录已正确创建
2. **权限设置**: 确保用户有权限访问Course Price Per Customer对象
3. **测试验证**: 部署后验证不同Customer看到不同的价格
4. **回退机制**: 当Customer特定价格不存在时，确保显示默认价格

## 兼容性

- **向后兼容**: 当没有Course Price Per Customer记录时，显示原有的默认价格
- **多用户支持**: 不同Customer用户看到各自的特定价格
- **多货币支持**: 保持原有的多货币显示功能

这个修改确保了CCM_Community_Course_Detail页面能够正确显示Customer特定的价格，提供了个性化的价格体验，同时保持了系统的稳定性和兼容性。
