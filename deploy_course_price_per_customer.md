# Course Price Per Customer 部署指南

## 部署步骤

### 1. 使用SFDX部署
```bash
# 部署所有新建和修改的文件
sfdx force:source:deploy -p force-app/main/default/objects/Course_Price_Per_Customer__c -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/layouts/Course_Price_Per_Customer__c-Course\ Price\ Per\ Customer\ Layout.layout-meta.xml -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/classes/CCM_CoursePricePerCustomerHandler.cls -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/classes/CCM_CoursePricePerCustomerHandler_Test.cls -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/classes/CCM_CourseRegisterCtl_CustomerPrice_Test.cls -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/triggers/CoursePricePerCustomerTrigger.trigger -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/classes/CCM_CourseRegisterCtl.cls -u your-org-alias
sfdx force:source:deploy -p force-app/main/default/layouts/Course_Arrangement__c-Course\ Arrangement\ Layout.layout-meta.xml -u your-org-alias
```

### 2. 或者一次性部署所有更改
```bash
sfdx force:source:deploy -p force-app/main/default -u your-org-alias
```

### 3. 运行测试
```bash
# 运行新建的测试类
sfdx force:apex:test:run -c -r human -t CCM_CoursePricePerCustomerHandler_Test -u your-org-alias
sfdx force:apex:test:run -c -r human -t CCM_CourseRegisterCtl_CustomerPrice_Test -u your-org-alias
```

## 验证部署

### 1. 检查对象是否创建成功
- 在Setup > Object Manager中查找"Course Price Per Customer"对象
- 确认所有字段都已创建：Course_Arrangement__c, Customer__c, Training_Price_Book__c, Price__c

### 2. 检查Layout是否正确
- 在Course Price Per Customer对象的Page Layouts中确认layout显示正确
- 在Course Arrangement的Page Layout中确认添加了Course Price Per Customer相关列表

### 3. 检查Trigger和Handler
- 在Setup > Apex Triggers中确认CoursePricePerCustomerTrigger已激活
- 在Setup > Apex Classes中确认CCM_CoursePricePerCustomerHandler类存在

### 4. 功能测试
1. 创建一个Course Arrangement记录
2. 在Course Arrangement页面，通过"Course Price Per Customer"相关列表添加一条记录
3. 选择Customer和Training Price Book
4. 保存后确认Price字段自动计算
5. 创建Course Register记录，确认使用了Customer特定的价格

## 回滚计划
如果需要回滚更改：

1. 删除Course Price Per Customer对象：
```bash
sfdx force:source:delete -p force-app/main/default/objects/Course_Price_Per_Customer__c -u your-org-alias
```

2. 恢复原始的CCM_CourseRegisterCtl.cls文件

3. 删除新建的类和trigger：
```bash
sfdx force:source:delete -p force-app/main/default/classes/CCM_CoursePricePerCustomerHandler.cls -u your-org-alias
sfdx force:source:delete -p force-app/main/default/triggers/CoursePricePerCustomerTrigger.trigger -u your-org-alias
```

## 注意事项
- 部署前请在Sandbox环境中充分测试
- 确保有足够的权限来创建自定义对象和字段
- 建议在维护窗口期间进行部署
- 部署后请通知相关用户新功能的使用方法
