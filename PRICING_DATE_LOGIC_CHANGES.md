# Course Price Per Customer 定价日期逻辑修改

## 修改概述
根据要求，修改了`CCM_CoursePricePerCustomerHandler`中的逻辑，使其在判断选择哪个`Traning_PriceBook_Entry__c`时使用Course_Arrangement__c上的`Course_Date__c`作为`pricingDate`，确保与Course Arrangement的价格计算逻辑完全一致。

## 具体修改内容

### 1. 修改前的逻辑
在之前的实现中，价格计算逻辑：
1. 使用`Date.today()`作为`pricingDate`
2. 在查询Training PriceBook Entry时使用当前日期过滤有效期
3. 在价格计算循环中不再检查日期有效性

### 2. 修改后的逻辑
新的实现中：
1. 从Course_Arrangement__c获取Course_Date__c作为定价日期
2. 在查询Training PriceBook Entry时不过滤日期，获取所有相关条目
3. 在价格计算循环中使用Course_Date__c检查每个条目的有效期

## 代码变更详情

### 文件：`CCM_CoursePricePerCustomerHandler.cls`

#### 变更1：方法签名修改
```apex
// 修改前
public void calculatePricePerCustomer(List<Course_Price_Per_Customer__c> lstCoursePricePerCustomer,
                                     List<String> lstPriceBookIDs,
                                     Date pricingDate,
                                     Set<String> setCourseArrangementIds)

// 修改后
public void calculatePricePerCustomer(List<Course_Price_Per_Customer__c> lstCoursePricePerCustomer,
                                     List<String> lstPriceBookIDs,
                                     Set<String> setCourseArrangementIds)
```

#### 变更2：查询逻辑修改
```apex
// 修改前
List<Traning_PriceBook_Entry__c> lstPriceEntry = [
    SELECT Id, UnitPrice__c, End_Date__c, Start_Date__c, Traning_PriceBook__c,
           Traning_PriceBook__r.IsActive__c, Course_Product__c
    FROM Traning_PriceBook_Entry__c
    WHERE Traning_PriceBook__r.IsActive__c = TRUE
    AND Start_Date__c <= :pricingDate
    AND End_Date__c >= :pricingDate
    AND Traning_PriceBook__c IN :lstPriceBookIDs
];

// 修改后
List<Traning_PriceBook_Entry__c> lstPriceEntry = [
    SELECT Id, UnitPrice__c, End_Date__c, Start_Date__c, Traning_PriceBook__c,
           Traning_PriceBook__r.IsActive__c, Course_Product__c
    FROM Traning_PriceBook_Entry__c
    WHERE Traning_PriceBook__r.IsActive__c = TRUE
    AND Traning_PriceBook__c IN :lstPriceBookIDs
];
```

#### 变更3：价格计算逻辑修改
```apex
// 修改前
for(Course_Price_Per_Customer__c objCoursePricePerCustomer : lstCoursePricePerCustomer){
    String productId = mapArrangementId2ProductId.get(objCoursePricePerCustomer.Course_Arrangement__c);

    if(String.isNotBlank(productId) && objCoursePricePerCustomer.Training_Price_Book__c != null){
        boolean priceFound = false;
        for(Traning_PriceBook_Entry__c entry : lstPriceEntry){
            if(entry.Traning_PriceBook__c == objCoursePricePerCustomer.Training_Price_Book__c
               && entry.Course_Product__c == productId){
                objCoursePricePerCustomer.Price__c = entry.UnitPrice__c;
                priceFound = true;
                break;
            }
        }
        // ... 错误处理
    }
}

// 修改后
for(Course_Price_Per_Customer__c objCoursePricePerCustomer : lstCoursePricePerCustomer){
    String productId = mapArrangementId2ProductId.get(objCoursePricePerCustomer.Course_Arrangement__c);
    Date courseDate = mapArrangementId2CourseDate.get(objCoursePricePerCustomer.Course_Arrangement__c);

    if(String.isNotBlank(productId) && objCoursePricePerCustomer.Training_Price_Book__c != null && courseDate != null){
        boolean priceFound = false;
        for(Traning_PriceBook_Entry__c entry : lstPriceEntry){
            if(entry.Traning_PriceBook__c == objCoursePricePerCustomer.Training_Price_Book__c
               && entry.Course_Product__c == productId
               && entry.Start_Date__c <= courseDate
               && entry.End_Date__c >= courseDate){
                objCoursePricePerCustomer.Price__c = entry.UnitPrice__c;
                priceFound = true;
                break;
            }
        }
        // ... 错误处理
    }
}
```

#### 变更4：重新添加Course_Date__c映射
```apex
// 重新添加了以下代码
Map<String, Date> mapArrangementId2CourseDate = new Map<String, Date>();
for(Course_Arrangement__c arrangement : lstCourseArrangements){
    mapArrangementId2CourseDate.put(arrangement.Id, arrangement.Course_Date__c);
}
```

## 测试更新

### 文件：`CCM_CoursePricePerCustomerHandler_Test.cls`

修改了测试方法`testPricingDateLogic()`来验证：
1. Course_Date__c设置为未来60天
2. Training PriceBook Entry的有效期从50天后开始，到70天后结束（覆盖Course_Date__c）
3. 验证价格计算使用的是Course_Date__c（60天后），而不是今天的日期

## 业务影响

### 优势
1. **逻辑一致性**：Course Price Per Customer和Course Arrangement使用相同的定价逻辑
2. **时效性保证**：价格计算基于课程的实际日期，确保价格的时效性
3. **用户体验一致**：提供一致的业务逻辑和用户体验

### 注意事项
1. **逻辑统一**：现在Course Price Per Customer和Course Arrangement都使用Course_Date__c进行价格计算
2. **时间敏感性**：Course Price Per Customer的价格基于课程的实际日期
3. **数据一致性**：需要确保Training PriceBook Entry的有效期覆盖课程日期

## 部署建议
1. 在Sandbox环境中充分测试新的定价逻辑
2. 验证现有的Course Price Per Customer记录不会受到影响
3. 确保用户了解新的定价行为
4. 监控部署后的价格计算结果
