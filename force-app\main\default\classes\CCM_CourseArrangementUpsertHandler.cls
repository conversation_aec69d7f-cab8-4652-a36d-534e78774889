/**
 * Author : Honey
 * Description : 在创建或者Price Book更新时修改价格
 */
public without sharing class CCM_CourseArrangementUpsertHandler implements Triggers.Handler{
    static boolean isRun = true;
    public void handle(){
        system.debug('进入handler');
        List<Course_Arrangement__c> lstCourseArrangement = new List<Course_Arrangement__c>();
        List<String> lstCourseArrangementPriceBookIDs = new List<String>();
        List<Course_Arrangement__c> lstCheckCourseArrangements = new List<Course_Arrangement__c>();
        Date PricinDate = Date.today();
        List<String> lstCourseSettingIds = new List<String>();
        if(isRun){
            if(Trigger.isInsert){
                for(Course_Arrangement__c objCourse : (List<Course_Arrangement__c>)Trigger.new){
                    // Handle Free Training logic
                    if(objCourse.Free_Training__c == true){
                        objCourse.Price__c = 0;
                    } else if(objCourse.Traning_PriceBook__c != null){
                        lstCourseArrangement.add(objCourse);
                        lstCourseArrangementPriceBookIDs.add(objCourse.Traning_PriceBook__c);
                        PricinDate = Date.valueOf(objCourse.Course_Date__c) ;
                        lstCourseSettingIds.add(objCourse.Training_Course_Setting__c);

                    }
                    checkDate(objCourse.Start_Time__c,objCourse.End_Time__c,objCourse);
                    lstCheckCourseArrangements.add(objCourse);
                }
            }
            if(Trigger.IsUpdate){
                Map<Id,Object> mapId2CourseOld =  (Map<Id, Object>)Trigger.oldMap;
                for(Course_Arrangement__c objCourse : (List<Course_Arrangement__c>)Trigger.new){
                    Course_Arrangement__c objOld = (Course_Arrangement__c)mapId2CourseOld.get(objCourse.Id);

                    // Handle Free Training logic
                    if(objCourse.Free_Training__c == true){
                        objCourse.Price__c = 0;
                    } else if(objCourse.Traning_PriceBook__c != objOld.Traning_PriceBook__c || objCourse.Course_Date__c !=objOld.Course_Date__c || objCourse.Free_Training__c != objOld.Free_Training__c){
                        if(objCourse.Traning_PriceBook__c != null){
                            lstCourseArrangement.add(objCourse);
                            lstCourseArrangementPriceBookIDs.add(objCourse.Traning_PriceBook__c);
                            PricinDate = Date.valueOf(objCourse.Course_Date__c) ;
                            lstCourseSettingIds.add(objCourse.Training_Course_Setting__c);
                        }
                    }
                    if(objCourse.Course_Date__c !=objOld.Course_Date__c ||  objCourse.Course_End_Date__c !=objOld.Course_End_Date__c ||
                      objCourse.Start_Time__c !=objOld.Start_Time__c || objCourse.End_Time__c !=objOld.End_Time__c ||
                      objCourse.Training_Location__c !=objOld.Training_Location__c ||
                      objCourse.Training_Course_Setting__c != objCourse.Training_Course_Setting__c){
                        lstCheckCourseArrangements.add(objCourse);
                    }
                    checkDate(objCourse.Start_Time__c,objCourse.End_Time__c,objCourse);

                }

            }

            if(lstCourseArrangement.size()>0){
                system.debug('计算');
                caculatePrice(lstCourseArrangement,lstCourseArrangementPriceBookIDs,PricinDate,lstCourseSettingIds);
            }
            if(lstCheckCourseArrangements.size()>0){
                system.debug('校验');
                checkUniqueArrangement(lstCheckCourseArrangements);
            }


        }

    }
    public void caculatePrice (List<Course_Arrangement__c> lstCourseArrangement,List<String> lstCourseArrangementPriceBookIDs ,Date pricingDate,List<String> lstCourseSettingIds){
        //根据PriceBookId查询Price Book Entry
        List<Traning_PriceBook_Entry__c> lstPriceEntry =  new List<Traning_PriceBook_Entry__c>();
        system.debug('lstCourseArrangementPriceBookIDs-->'+lstCourseArrangementPriceBookIDs);
        lstPriceEntry = [
            SELECT Id, UnitPrice__c,End_Date__c,Start_Date__c,Traning_PriceBook__c,Traning_PriceBook__r.IsActive__c,Course_Product__c
            FROM Traning_PriceBook_Entry__c WHERE Traning_PriceBook__r.IsActive__c = TRUE
            AND Start_Date__c <= : pricingDate AND End_Date__c >= :pricingDate
            AND Traning_PriceBook__c IN : lstCourseArrangementPriceBookIDs
        ];
        if(lstPriceEntry == null || lstPriceEntry.size() == 0 ){
            lstCourseArrangement[0].addError(Label.Service_Training_No_Product);
        }
        //遍历list获取Map  Id到价格得映射
        Map<String,Decimal> mapProductId2Entry = new Map<String,Decimal>();
        for(Traning_PriceBook_Entry__c objEntry : lstPriceEntry){
            mapProductId2Entry.put(objEntry.Course_Product__c, objEntry.UnitPrice__c);
        }
        system.debug('mapProductId2Entry-->'+mapProductId2Entry);
        List<Training_Course_Setting__c> lstTraining = [
            SELECT Id,Course_Name__c FROM Training_Course_Setting__c WHERE Id IN  : lstCourseSettingIds
        ];
        Map<String,String> mapId2ProductId = new Map<String,String>();
        for(Training_Course_Setting__c objCourseSetting : lstTraining){
            mapId2ProductId.put(objCourseSetting.Id, objCourseSetting.Course_Name__c);
        }
        for(Course_Arrangement__c objCouse  : lstCourseArrangement){
            String productId = mapId2ProductId.get(objCouse.Training_Course_Setting__c);
            if(String.isNotBlank(productId)){
                objCouse.Price__c = mapProductId2Entry.get(productId);
                if(objCouse.Price__c == null){
                    objCouse.addError(Label.Service_Training_No_Product);
                }

            }else{
                objCouse.addError(Label.Service_Training_No_Product);
            }
        }
    }
    public void checkDate(Time startDate, Time endDate, Course_Arrangement__c objCourse){
        system.debug('checkDate');

        // Check if start time is after end time
        if(startDate > endDate){
            system.debug('报错');
            objCourse.addError(Label.Check_Arrangement_Date);
            return;
        }

        // Check if course end date is before course start date
        if(objCourse.Course_End_Date__c != null && objCourse.Course_Date__c != null &&
           objCourse.Course_End_Date__c < objCourse.Course_Date__c){
            objCourse.addError('Course End Date cannot be before Course Start Date');
            return;
        }

        // Set default end date if not provided (single day course)
        if(objCourse.Course_End_Date__c == null && objCourse.Course_Date__c != null){
            objCourse.Course_End_Date__c = objCourse.Course_Date__c;
        }

        if(objCourse.Course_Date__c != null && objCourse.Start_Time__c != null){
            objCourse.Course_Start_Time__c = DateTime.newInstance(objCourse.Course_Date__c, objCourse.Start_Time__c);
        } else {
            objCourse.Course_Start_Time__c = null;
        }

        Date endDate_calc = objCourse.Course_End_Date__c != null ? objCourse.Course_End_Date__c : objCourse.Course_Date__c;
        if(endDate_calc != null && objCourse.End_Time__c != null){
            objCourse.Course_End_Time__c = DateTime.newInstance(endDate_calc, objCourse.End_Time__c);
        } else {
            objCourse.Course_End_Time__c = null;
        }
    }
    public void checkUniqueArrangement(List<Course_Arrangement__c> lstNewCourseArrangement){
        //遍历Arrangement .获取数据库中存在的Arrangement
        List<String> lstSetingId = new  List<String>();
        List<Date> lstCourseDate = new  List<Date>();
        List<Time> lstStartTime = new  List<Time>();
        List<Time> lstEndTime = new  List<Time>();
        List<String> lstLocationIds = new  List<String>();
        List<String> lstCourseArrangementId = new List<String>();
        List<Date> lstCourseEndDate = new  List<Date>();
        for(Course_Arrangement__c objCourse : lstNewCourseArrangement){
            lstSetingId.add(objCourse.Training_Course_Setting__c);
            lstCourseDate.add(objCourse.Course_Date__c);
            lstCourseEndDate.add(objCourse.Course_End_Date__c != null ? objCourse.Course_End_Date__c : objCourse.Course_Date__c);
            lstStartTime.add(objCourse.Start_Time__c);
            lstEndTime.add(objCourse.End_Time__c);
            if(String.isNotBlank(objCourse.Id)){
                lstCourseArrangementId.add(objCourse.Id);
            }
            lstLocationIds.add(objCourse.Training_Location__c);
        }
        List<Course_Arrangement__c> lstExistArrangement = new List<Course_Arrangement__c>();
        system.debug('lstCourseArrangementId-->'+lstCourseArrangementId);
        if(lstCourseArrangementId == null || lstCourseArrangementId.size() == 0){
            system.debug('插入-->');
            lstExistArrangement = [
                SELECT Id,Name,Training_Course_Setting__c,Course_Date__c,Course_End_Date__c,End_Time__c,Start_Time__c
                FROM Course_Arrangement__c WHERE Training_Course_Setting__c IN : lstSetingId AND
                Course_Date__c IN : lstCourseDate AND
                (Course_End_Date__c IN : lstCourseEndDate OR Course_End_Date__c = null) AND
                Start_Time__c IN : lstStartTime AND End_Time__c IN : lstEndTime
                AND  Training_Location__c IN :lstLocationIds
            ];
        }else{
            system.debug('更新-->');
            lstExistArrangement = [
                SELECT Id,Name,Training_Course_Setting__c,Course_Date__c,Course_End_Date__c,End_Time__c,Start_Time__c
                FROM Course_Arrangement__c WHERE Training_Course_Setting__c IN : lstSetingId AND
                Course_Date__c IN : lstCourseDate AND
                (Course_End_Date__c IN : lstCourseEndDate OR Course_End_Date__c = null) AND
                Start_Time__c IN : lstStartTime AND End_Time__c IN : lstEndTime
                AND Id NOT IN :lstCourseArrangementId  AND  Training_Location__c IN :lstLocationIds
            ];
        }

        system.debug('lstExistArrangement-->'+lstExistArrangement);
        if(lstExistArrangement != null &&  lstExistArrangement.size()>0){
            //表示不唯一。需要报错
            lstNewCourseArrangement[0].addError(Label.check_Unique_Arrangement);
        }



    }
    public CCM_CourseArrangementUpsertHandler() {

    }
}