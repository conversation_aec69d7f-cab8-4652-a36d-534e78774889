/**
 * Honey 
 * Arrangement Or Setting 头上或者行上publish Or UnPublish
 * 2023-09-19
 */
public without sharing class CCM_CourseArrangementPublishCtl {
    //查询头上关联的可以publish 的所有行数据
    @AuraEnabled
    public static List<Map<String,Object>> queryAvaliablePublishList(String JsonData){
        system.debug('前端请求参数--->'+JsonData);
        try {
            Map<String,String> mapFeild2ValueReuqest = (Map<String,String>)JSON.deserialize(JsonData, Map<String,String>.class);
            String courseSettingId = mapFeild2ValueReuqest.get('courseSettingId');
            String operation  = mapFeild2ValueReuqest.get('operation');
            
            List<Course_Arrangement__c> lstArrangement  = new List<Course_Arrangement__c>();
            if(operation == 'launch'){
                //根据CourseSettingId查询所有不为publish的Arrangement 
                lstArrangement = [
                    SELECT Id,Name,End_Time__c,Start_Time__c,Course_Date__c,Course_End_Date__c,Training_Location__c,Training_Location__r.Name,
                    Avaliable_Slot__c,Training_Course_Setting__c ,Status__c
                    FROM Course_Arrangement__c WHERE  Training_Course_Setting__c = : courseSettingId ORDER BY RollUp_Status__c DESC
                ];
            }else if(operation == 'unPublish'){
                //根据CourseSettingId查询所有不为publish的Arrangement 
                lstArrangement = [
                    SELECT Id,Name,Start_Time__c,Course_Date__c,Course_End_Date__c,Training_Location__c,End_Time__c,
                    Avaliable_Slot__c,Training_Course_Setting__c ,Status__c,Training_Location__r.Name
                    FROM Course_Arrangement__c WHERE  Training_Course_Setting__c = : courseSettingId ORDER BY RollUp_Status__c ASC
                ];

            }
            List<Map<String,Object>>  lstcourseInfo = new List<Map<String,Object>>();
            if(lstArrangement != null && lstArrangement.size()>0 ){
                for(Course_Arrangement__c objArrangemnt : lstArrangement){
                    Map<String,Object> mapFeild2Value = new Map<String,Object>();
                    mapFeild2Value.put('arrangementId', objArrangemnt.Id);
                    mapFeild2Value.put('arrangementNo', objArrangemnt.Name);
                    mapFeild2Value.put('arrangementDate', objArrangemnt.Course_Date__c);
                    mapFeild2Value.put('arrangementStartTime', objArrangemnt.Start_Time__c ==  null ? null : DateTime.newInstance(objArrangemnt.Course_Date__c, objArrangemnt.Start_Time__c).format('HH:mm'));
                    mapFeild2Value.put('arrangementEndTime',objArrangemnt.End_Time__c ==  null ? null :  DateTime.newInstance(objArrangemnt.Course_End_Date__c, objArrangemnt.End_Time__c).format('HH:mm'));
                    // mapFeild2Value.put('arrangementEndDate', objArrangemnt.Course_End_Date__c);
                    mapFeild2Value.put('arrangementEndTime', objArrangemnt.End_Time__c);
                    // Format date range display
                    // String dateDisplay = '';
                    // if(objArrangemnt.Course_Date__c != null){
                    //     if(objArrangemnt.Course_End_Date__c != null && objArrangemnt.Course_End_Date__c != objArrangemnt.Course_Date__c){
                    //         dateDisplay = objArrangemnt.Course_Date__c.format() + ' - ' + objArrangemnt.Course_End_Date__c.format();
                    //     } else {
                    //         dateDisplay = objArrangemnt.Course_Date__c.format();
                    //     }
                    // }
                    // mapFeild2Value.put('arrangementDateDisplay', dateDisplay);
                    mapFeild2Value.put('arrangementLocation', objArrangemnt.Training_Location__r.Name);
                    mapFeild2Value.put('arrangementAvaliable', objArrangemnt.Avaliable_Slot__c);
                    mapFeild2Value.put('arrangementStatus', objArrangemnt.Status__c);
                    lstcourseInfo.add(mapFeild2Value);
                }
            }
            return lstcourseInfo;

        } catch (Exception e) {
            throw new AuraHandledException(e.getMessage());
        }
    }
    @AuraEnabled
    public static String publishOrUnPublishArrangement(List<String>  lstListArrangementIds ,String operation,Boolean isAll,String courseSettingId){
        system.debug('前端参数lstListArrangementIds--->'+lstListArrangementIds);
        system.debug('前端参数operation--->'+operation);
        system.debug('前端参数isAll--->'+isAll);
        system.debug('前端参数courseSettingId--->'+courseSettingId);
        try {
            //通过ArrangementIds查询ArrangementInfo 
            List<Course_Arrangement__c> lstCourseArrangementInfo = [
                SELECT Id,Status__c,Training_Course_Setting__c FROM Course_Arrangement__c WHERE Id IN : lstListArrangementIds
            ];
            //通过CourseSettingId查询CourseSetiing Info 
            if(courseSettingId==''&&lstCourseArrangementInfo.size()>0){
                courseSettingId=lstCourseArrangementInfo[0].Training_Course_Setting__c;
            }
            Training_Course_Setting__c objTrainingCourse = new Training_Course_Setting__c();
            objTrainingCourse = [
                SELECT Id,Status__c FROM Training_Course_Setting__c WHERE Id = : courseSettingId
            ];
            if(operation == 'unPublish' && isAll){
                //头上下架必须手动点击下架
                objTrainingCourse.Status__c = 'Unpublish';
            }
            if(lstCourseArrangementInfo != null && lstCourseArrangementInfo.size()>0){
                for(Course_Arrangement__c objCourseArrangement : lstCourseArrangementInfo){
                    if(operation == 'launch'){
                        objCourseArrangement.Status__c = 'Launched';
                        //有一个为launch则头为launch 
                        objTrainingCourse.Status__c = 'Launched';
                    }else if(operation == 'unPublish'){
                        objCourseArrangement.Status__c = 'Unpublish';
                    }
                    
                }
                update lstCourseArrangementInfo;
                
            }
          
            
            
            update objTrainingCourse;
            return CCM_Constants.SUCCESS;
           
            
        } catch (Exception e) {
            system.debug('报错信息--->'+e.getMessage()+'报错行数---->'+e.getLineNumber());
            throw new AuraHandledException(e.getMessage());
        }
    }
    public CCM_CourseArrangementPublishCtl() {

    }
}