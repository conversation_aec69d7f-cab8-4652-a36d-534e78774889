<!--- Created by <PERSON> on 9/21/2023.-->

<aura:component extends="forceCommunity:navigationMenuBase" implements="force:appHostable,flexipage:availableForAllPageTypes,flexipage:availableForRecordHome,force:hasRecordId,forceCommunity:availableForAllPageTypes,forceCommunity:searchInterface,forceCommunity:themeLayout"
                description="CCM_CourseRegisterCtl"
                controller="CCM_CourseRegisterCtl">
<aura:attribute name="TrainingCourseList" type="List" default="[]"/>
<aura:attribute name="TrainingCourseInfo" type="Map" default="{}"/>
<aura:attribute name="currentStep" type="Integer" default="1"/>
<aura:attribute name="stepsList" type="List"/>
<aura:attribute name="customerId" type="String" default=""/>
<aura:attribute name="CourseArrangementInfo" type="Map" default="{}"/>
<aura:attribute name="VAT" type="String" default=""/>
<aura:attribute name="ArrangementId" type="String" default=""/>
<aura:attribute name="CustomerName" type="String" default=""/>
<aura:attribute name="trainingid" type="String" default=""/>
<aura:attribute name="BillToAddress" type="Map" default="{}"/>
<aura:attribute name="ShipToAddress" type="Map" default="{}"/>
<aura:attribute name="lockselectcustomer" type="Boolean" default="false"/>
<aura:attribute name="paymentTerm" type="String" default=""/>
<aura:attribute name="TrainingCourseDetail" type="Map" default="{}"/>
<aura:attribute name="currencySymbol" type="String" default=""/>
<aura:attribute name="TrainingAmount" type="Integer" default=""/>
<aura:attribute name="TotalAmount" type="Integer" default=""/>
<aura:attribute name="hasArrangement" type="Boolean" default="true"/>
<aura:attribute name="RegisterId" type="String" default=""/>
<aura:attribute name="pcs" type="Integer" default="1"/>
<aura:attribute name="pcsList" type="List" default="[{value:'',index:1}]"/>
<aura:attribute name="ClickProposePupup" type="Boolean" default="false"/>
<aura:attribute name="isBusy" type="Boolean" default="false"/>
<aura:attribute name="SbumitProposeInfo" type="Map" default="{}"/>
<aura:attribute name="CoursePrice" type="String" default=""/>
<aura:handler name="change" value="{!v.BillToAddress}" action="{!c.changeBillToAddress}"/>
<aura:handler name="change" value="{!v.ShipToAddress}" action="{!c.changeShipToAddress}"/>
<aura:handler name="init" value="{!this}" action="{!c.doInit}"/>
<aura:attribute name="noArrangementMessage" type="String" default="{!$Label.c.No_Arrangement}"/>
<aura:attribute name="isDealer" type="Boolean" default="false" />

<lightning:spinner size="large" variant="brand" class="{! v.isBusy ? 'slds-show slds-is-fixed loading-wrap' : 'slds-hide' }"/>
    <!-- 进度条 -->
    <div style="padding: 20px 5%;margin-bottom: 10px;">
        <c:CCM_ProcessStep currentStep="{!v.currentStep}" stepName="{!v.stepsList}" />
    </div>
    <!-- 第一步/选择课程 -->
    <div style="margin: 0 5%;border: 2px solid #e1e1e1;border-radius: 5px;padding: 20px;">
        <aura:if isTrue="{!v.currentStep == 1}">
            <div>
                <!-- Banner info -->
                <div>
                    <table class="data-info">
                        <tr class="table-title">{!$Label.c.CCM_CourseDetail}</tr>
                    </table>
                </div>
                <div style="justify-content:space-between;margin-bottom: 40px;">
                    <div class="main-info">
                        <p>
                            <span>{!$Label.c.CCM_CourseNo}:</span>{!v.TrainingCourseInfo.courseNo}
                        </p>
                        <p>
                            <span>{!$Label.c.CCM_CourseName}:</span>{!v.TrainingCourseInfo.courseName}
                        </p>
                        <p>
                            <span>{!$Label.c.CCM_CourseDescription}:</span>
                            <div class="richtext"><aura:unescapedHtml value="{!v.TrainingCourseInfo.courseDescription}"/></div>
                        </p>
                    </div>
                    <!-- <div style="height: 200px;width: 400px;background-color: #e7e7e7;">
                        如果需要课程封面,加在这里
                    </div> -->
                </div>
            <!-- information -->
                <c:CCM_Section title="{!$Label.c.CCM_CourseArrangement}" expandable="true">
                    <div class="Basic-info" style="padding: 10px 4%;">
                        <div style="display: flex;justify-content:space-between;">
                            <aura:if isTrue="{!v.hasArrangement == false}">
                                <p> {!v.noArrangementMessage} </p>
                            </aura:if>
                            <aura:if isTrue="{!v.hasArrangement == true}">
                            <table class="data-info">
                                <tr class="t-header">
                                    <th style="width: 20%;"></th>
                                    <th style="width: 14%;">{!$Label.c.CCM_TrainingStartTime}</th>
                                    <th style="width: 16%;">{!$Label.c.CCM_TrainingEndTime}</th>
                                    <th style="width: 16%;">{!$Label.c.CCM_TrainingLocation}</th>
                                    <th>{!$Label.c.CCM_Slot}
                                        <span style="position: relative;top: -4px;">
                                            <lightning:helptext class="icon-size" content="Available/Total"/>
                                        </span>
                                    </th>
                                    <th>{!$Label.c.CCM_Price} </th>
                                    <th></th>
                                </tr>
                                <aura:if isTrue="{!v.TrainingCourseInfo.lstArrangemtInfo.length}">
                                    <aura:iteration items="{!v.TrainingCourseInfo.lstArrangemtInfo}" var="item" indexVar="index">
                                        <tr class="{!v.ArrangementId == item.arrangementId ? 'bgcgery t-body' : 't-body'}"  style="border-bottom: #cdcdcd solid 2px;">
                                            <td style="cursor: pointer;">
                                                <span style="font-weight: 700;" onclick="{!c.showProduct}" name="{!index}">
                                                    <lightning:icon iconName="utility:chevrondown" size="xx-small" id="{!index}" class="collapse_icn"/>
                                                    {!$Label.c.CCM_AgreementNo}: &nbsp;&nbsp;
                                                </span>
                                                {!item.arrangementNo}
                                            </td>
                                            <td>{!item.arrangementStartTime}</td>
                                            <td>{!item.arrangementendTime}</td>
                                            <td style="text-align: left;">
                                                <aura:iteration items="{!item.arrangementLocationList}" var="location">
                                                    <div>{!location}</div>
                                                </aura:iteration>
                                            </td>
                                            <td>{!item.arrangementSlot}</td>
                                            <td>
                                                <lightning:formattedNumber value="{!item.arrangementprice}" style="currency" currencyCode="{!v.currencySymbol}"/>
                                            </td>
                                            <td style="text-align: right;padding: 10px 0;"><lightning:button disabled="{!item.disableBtn}" name="{!index}" class="{!item.disableBtn ? 'slds-p-horizontal_x-large disable' : 'slds-p-horizontal_x-large'}" variant="brand" label="{!$Label.c.CCM_Register}" onclick="{!c.doRegister}" /></td>
                                        </tr>
                                        <aura:if isTrue="{!item.lstProductInfo.length}">
                                            <tr class="{!item.showProduct ? 'showproduct' : 'hideproduct'}" style="line-height: 36px;">
                                                <th></th>
                                                <th>{!$Label.c.CCM_RelatedProduct}</th>
                                                <th>{!$Label.c.CCM_ProductDescription}</th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                                <th></th>
                                            </tr>
                                            <aura:iteration items="{!item.lstProductInfo}" var="Product">
                                                <tr class="{!item.showProduct ? 'showproduct product-tr' : 'hideproduct product-tr'}">
                                                    <td></td>
                                                    <td>{!Product.productName}</td>
                                                    <td style="text-wrap: nowrap;position: absolute;">{!Product.productDescription}</td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                            </aura:iteration>
                                        </aura:if>
                                    </aura:iteration>
                                </aura:if>
                            </table>
                        </aura:if>
                        </div>
                    </div>
                </c:CCM_Section>
                <aura:if isTrue="{! !v.isDealer}">
                    <div style="text-align:right;margin:20px 4% 0 0;">
                        <lightning:button class="slds-p-horizontal_x-large bgcblack slds-m-right_x-small" variant="brand" label="{!$Label.c.CCM_ProposeNewArrangement}" onclick="{!c.ClickPropose}" />
                    </div>
                </aura:if>
            </div>
        </aura:if>
        <!-- 第二部/选择pcs -->
        <aura:if isTrue="{!v.currentStep == 2}">
            <!-- 主体部分 -->
            <div style="padding: 5px;">
                <div>
                    <!-- Basic information -->
                    <c:CCM_Section title="Basic Information" expandable="true">
                        <div class="Basic-info" style="padding: 10px 4%;">
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_Customer}:</span>{!v.CustomerName}</p>
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_TrainingCourse}:</span>{!v.TrainingCourseInfo.courseName}</p>
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_TrainingStartTime}:</span>{!v.CourseArrangementInfo.arrangementStartTime}</p>
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_TrainingEndTime}:</span>{!v.CourseArrangementInfo.arrangementendTime}</p>
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_TrainingLocation}:</span>
                                <div style="display: inline-block; vertical-align: top;">
                                    <aura:iteration items="{!v.CourseArrangementInfo.arrangementLocationList}" var="location">
                                        <div style="text-align: left;">{!location}</div>
                                    </aura:iteration>
                                </div>
                            </p>
                            <p style="width: 50%;display: inline-block;">
                                <div style="display: flex;">
                                    <span style="font-weight: 600; width: 200px;">{!$Label.c.CCM_TrainingDescription}:</span>
                                    <div class="richtext"><aura:unescapedHtml value="{!v.TrainingCourseInfo.courseDescription}"/></div>
                                </div>
                            </p>
                            <p style="width: 50%;display: inline-block;height: 60px;"><span class="field-st1">{!$Label.c.CCM_Price_PCS}:</span><lightning:formattedNumber value="{!v.CoursePrice}" style="currency" currencyCode="{!v.currencySymbol}"/></p>
                            <p style="width: 50%;display: flex;height: 60px;"><span class="field-st1">{!$Label.c.CCM_PCS}:</span>
                                <div style="display: flex;margin-left: 2px;position: relative;top: -2px;">
                                    <lightning:button class="sub" label="-" onclick="{!c.handleSubPCS}"></lightning:button>
                                    <lightning:input class="pcsinput" type="text" label="" value="{!v.pcs}" onchange="{!c.searchHeaderPromotionByCode}"/>
                                    <lightning:button class="add" label="+" onclick="{!c.handleAddPCS}"></lightning:button>
                                </div>
                            </p>
                        </div>

                        <aura:if  isTrue="{!v.pcsList.length}">
                            <p class="Participant-title">{!$Label.c.CCM_InputNameOfParticipants}</p>
                        </aura:if>
                        <div style="padding: 10px 3%;display: flex;flex-wrap: wrap;">
                            <!-- pcs的框框 -->
                            <aura:iteration items="{!v.pcsList}" var="item" indexVar="index">
                                <lightning:input class="pcsListinput" type="text" label="{!$Label.c.CCM_Participants + '&nbsp;' + item.index}" value="{!item.participants}" onchange=""/>
                                <lightning:input class="pcsListinput" type="text" label="{!$Label.c.CCM_EmailAddress + '&nbsp;' + item.index}" value="{!item.email}" onchange=""/>
                                <lightning:input class="pcsListinput" type="text" label="{!$Label.c.CCM_TeleSMS + '&nbsp;' + item.index}" value="{!item.SMS}" onchange=""/>
                                <lightning:input class="pcsListinput" type="text" label="{!$Label.c.CCM_Remark + '&nbsp;' + item.index}" value="{!item.remark}" onchange=""/>
                            </aura:iteration>
                        </div>
                    </c:CCM_Section>

                    <!-- Payment -->
                    <c:CCM_Section title="Payment" expandable="true">
                        <div class="Payment" style="padding: 10px 4%;">
                            <p style="width: 50%;display: inline-block;overflow: hidden;height: 60px;vertical-align: text-top;"><span class="field-st1">{!$Label.c.Order_PaymentTerm}:</span>{!v.paymentTerm}</p>
                            <p style="width: 50%;display: inline-block;overflow: hidden;height: 60px;"></p>
                            <p style="width: 50%;display: inline-block;height: 60px;position: relative;top: -26px;" class="bill-address-input"><span class="field-st1">{!$Label.c.Billing_address}:</span>
                                <c:CCM_Community_LookUp
                                fieldName="Select Bill To Address"
                                fieldNameLabel="{!$Label.c.CCM_SelectBillToAddress}"
                                selectedValue="{!v.BillToAddress}"
                                customerId="{!v.customerId}"
                                onSelect="{!c.changeBillToAddress}"
                                isDisabled="{!v.lockselectcustomer}"
                                class="bill-address"
                                />
                                <div style="position: absolute;left: 180px;top:40px;">{!v.BillToAddress.name}</div>
                                <div style="position: absolute;left: 180px;top:60px;">{!v.BillToAddress.country}</div>
                            </p>
                            <p style="width: 50%;display: inline-block;overflow: hidden;height: 60px;"></p>
                            <p style="width: 50%;display: inline-block;height: 60px;position: relative;" class="bill-address-input"><span class="field-st1">Shipping Address:</span>
                                <c:CCM_Community_LookUp
                                class="bill-address"
                                fieldName="Select Ship To Address"
                                fieldNameLabel="{!$Label.c.CCM_SelectShipToAddress}"
                                selectedValue="{!v.ShipToAddress}"
                                customerId="{!v.customerId}"
                                onSelect="{!c.changeShipToAddress}"
                                isDisabled="{!v.lockselectcustomer}"
                                />
                                <div style="position: absolute;left: 180px;top:40px;">{!v.ShipToAddress.name}</div>
                                <div style="position: absolute;left: 180px;top:60px;">{!v.ShipToAddress.country}</div>
                            </p>

                            <div class="slds-clearfix slds-m-top--medium" style="margin-right: 4%;">
                                <div class="slds-grid slds-float--right">
                                <div class="slds-text-align--right">
                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.CCM_TrainingAmount}:&nbsp;</div>
                                    <div class="slds-truncate ccm_padding" title="">{!$Label.c.Order_VAT}:&nbsp;</div>
                                    <div class="slds-border_bottom ccm_paddingTop" />
                                    <div class="slds-truncate ccm_padding ccm_paddingTop" title=""><strong>{!$Label.c.Order_TotalDueAmount}:&nbsp;</strong></div>
                                </div>
                                <div>
                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.pcs * v.CoursePrice}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                                    <div class="slds-truncate" title=""><strong><lightning:formattedNumber value="{!v.pcs * v.VAT}" style="currency" currencyCode="{!v.currencySymbol}"/></strong></div>
                                    <div class="slds-border_bottom ccm_paddingTop" />
                                    <div class="slds-truncate ccm_paddingTop" title=""><strong><lightning:formattedNumber value="{!v.pcs * (v.CoursePrice + v.VAT)}" style="currency" currencyCode="{!v.currencySymbol}"/> </strong></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </c:CCM_Section>
                </div>
            </div>
        </aura:if>
        <!-- 第三部/预览 -->
        <aura:if isTrue="{!v.currentStep == 3}">
            <c:CCM_TrainingOrderView recordId="{!v.RegisterId}" showSpinner="false">
            </c:CCM_TrainingOrderView>
        </aura:if>
    </div>

    <!-- 点击建议课程时间的按钮的弹窗 -->
    <!-- 弹窗 start -->
    <aura:if isTrue="{!v.ClickProposePupup}">
        <div>
            <section role="dialog" tabindex="-1" aria-labelledby="modal-heading-01" aria-modal="true" aria-describedby="modal-content-id-1" class="slds-modal slds-fade-in-open">
                <div class="slds-modal__container" style="width: 50rem !important; max-width: 60rem !important; position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                    <div class="modal-header slds-modal__header">
                        <button class="slds-button slds-modal__close closeIcon slds-button_icon-bare slds-button_icon-inverse" title="Close" onclick="{!c.cancelProposeEvent}">
                            <lightning:icon iconName="utility:close" alternativeText="close!" variant="close" class = "modal_close"/>
                            <span class="slds-assistive-text">{!$Label.c.CCM_Close}</span>
                        </button>
                        <h2 id="modal-heading-01" class="slds-text-heading_medium slds-hyphenate">{!$Label.c.CCM_ProposeArrangement}</h2>
                    </div>
                    <div class="slds-modal__content" id="modal-content-id-1" style="overflow:initial;">
                        <div class="content-wrap">
                            <!-- Select Propose Arrangement -->
                            <c:CCM_Community_LookUp
                                fieldName="Select Training Location"
                                fieldNameLabel="{!$Label.c.CCM_SelectTrainingLocation}"
                                selectedValue="{!v.SbumitProposeInfo.location}"
                                class="Propose"
                            />
                            <lightning:input class="Propose" type="date" name="input2" label="{!$Label.c.CCM_Date}" value="{!v.SbumitProposeInfo.date}" />
                            <lightning:input class="Propose" type="time" name="input4" label="{!$Label.c.CCM_StartTime}" value="{!v.SbumitProposeInfo.StartTime}" />
                            <lightning:input class="Propose" type="time" name="input4" label="{!$Label.c.CCM_EndTime}" value="{!v.SbumitProposeInfo.EndTime}" />
                            <lightning:textarea class="pup-up-propose" name="comments" value="{!v.SbumitProposeInfo.description}" label="{!$Label.c.Comments}" />
                        </div>
                    </div>
                    <footer class="slds-modal__footer">
                        <div class="footer-wrap">
                            <lightning:button class="" variant="brand" label="{!$Label.c.CCM_Submit}" title="{!$Label.c.CCM_Submit}" onclick="{!c.onclickProposeSubmit}"/>
                            <lightning:button class="" variant="brand-outline" label="{!$Label.c.CCM_Cancel}" title="Cancel" onclick="{!c.cancelProposeEvent}"/>
                        </div>
                    </footer>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
        </div>
    </aura:if>
    <!-- 弹窗 end -->

    <!-- 底部按钮 -->
    <div style="text-align: center;margin: 30px 0 80px 0;">
        <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Cancel}" onclick="{!c.onCancel}" />
        <aura:if isTrue="{!v.currentStep != 1}">
            <lightning:button class="slds-p-horizontal_x-large slds-m-right_x-small" label="{!$Label.c.CCM_Prev}" onclick="{!c.onPrevious}" />
        </aura:if>
        <aura:if isTrue="{!v.currentStep != 1}">
            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_SaveAndExit}" onclick="{!c.saveAsDraft1}" />
        </aura:if>
        <aura:if isTrue="{!v.currentStep == 2}">
            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!$Label.c.CCM_Save}" onclick="{!c.saveAsDraft2}" />
        </aura:if>
        <aura:if isTrue="{!v.currentStep != 1}">
            <lightning:button class="slds-p-horizontal_x-large" variant="brand" label="{!v.currentStep == 3 ? $Label.c.CCM_Submit : $Label.c.CCM_Next}" onclick="{!c.nextStep}" />
        </aura:if>
    </div>
</aura:component>