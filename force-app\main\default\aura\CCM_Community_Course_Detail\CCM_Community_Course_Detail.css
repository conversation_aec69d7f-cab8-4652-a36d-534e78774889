.THIS {
    padding: 0 5%;
}
.THIS .main-info{
    /* font-size: 15px; */
    line-height: 40px;
    margin-left: 4%;
}
.THIS .main-info > p {
    display: flex;
}
.THIS .main-info > p > span{
    display: inline-block;
    width: 19%;
    font-weight: 750;
}
/* .THIS .data-info{
    font-size: 14px;
} */
.THIS .data-info td, .THIS .data-info th{
    text-align: left;
}
.THIS .disable{
    background-color: #d7d7d7 !important;
    color: #8b8b8b !important;
    border: 1px #d7d7d7 solid !important;
}
.THIS .bgcblack{
    background-color: black !important;
    border: 1px solid black !important;
    margin: 0;
}
.THIS .table-title{
    display: table-caption;
    text-align: center;
    border-radius: 5px;
    font-size: 14px;
    background-color: gainsboro;
    padding: 5px;
    margin-bottom: 25px;
    padding-left: 30px;
    text-align: left;
    font-weight: 600;
}
.THIS .cCCM_Section .section__title__content{
    font-size: 14px;
}
.THIS .hideproduct{
    display: none;
}
.THIS .showproduct{
    display: table-row;
}
.THIS .sub, .THIS .add{
    margin-top: -5px;
    height: 32px;
}
.THIS .pcsinput{
    margin-top: -24px;
    width: 50px;
}
.THIS .pcsListinput{
    width: 22%;
    margin: 0 1% 0 10px;
}
.THIS .pcsListinput .slds-form-element__label{
    font-size: 12px !important;
}
.THIS .bill-address{
    display: inline-block;
    padding-bottom: 20px;
}
.THIS .field-st1{
    font-weight: 600;
    display: inline-block;
    width: 180px;
}
.THIS .bill-address .slds-form-element__label{
    display: none;
}
.THIS .Participant-title{
    margin-left: 4%;
    font-size: 12px;
    color: red;
}
.THIS .Propose{
     width: 50%;
     display: inline-block !important;
     padding-right: 4% !important;
}

.THIS .content-wrap{
    padding: 20px;
}
.THIS .pup-up-propose .slds-form-element__label{
    font-size: 16px;
}
.THIS .pup-up-propose .slds-textarea{
    height: 100px;
}
.THIS .cCCM_Community_LookUp .slds-form-element__label,.THIS .cCCM_Community_LookUp .slds-form-element__control{
    display: block;
}
.THIS .bill-address-input .slds-form-element__label{
    display: none;
}
.THIS .product-tr{
    line-height: 36px;
    background-color: #ebebeb;
    border-bottom: 1px solid #dddddd;
}
.THIS .richtext {
    display: flex;
    flex-direction: column;
    justify-content: start;
}

.THIS .richtext ul {
    padding-left: 30px;
}

.THIS .richtext li{
    /* display: flex !important; */
    list-style: disc !important;
}
.THIS .richtext img{
    max-width: 200px;
}
.THIS .bgcgery{
    background-color: rgb(241, 241, 241);
}