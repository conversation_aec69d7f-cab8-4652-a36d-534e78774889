# CCM_TrainingOrderView 价格显示逻辑修改

## 修改概述
为了支持Course Price Per Customer的多价格册逻辑，修改了CCM_TrainingOrderView页面相关的价格显示逻辑，使其能够显示Customer特定的价格。

## 修改的文件和方法

### 1. CCM_CourseRegisterPreviewCtl.cls

#### 修改的方法：

##### 1.1 queryRegisterInfo方法
- **功能**: 查询Course Register详细信息，在Training Order View页面显示
- **修改内容**: 
  - 在Course Register Item的价格显示中，优先使用Customer特定价格
  - 如果没有Customer特定价格，则使用默认的Course Arrangement价格

```apex
// 修改前
mapItemInfo.put('listPrice', objItem.Course_Arrangement__r.Price__c);

// 修改后
// Get customer-specific price if available, otherwise use default arrangement price
Decimal customerPrice = CCM_CourseRegisterCtl.getCustomerSpecificPrice(objItem.Course_Arrangement__c, objCourseRegister.Customer__c);
Decimal finalPrice = customerPrice != null ? customerPrice : objItem.Course_Arrangement__r.Price__c;
mapItemInfo.put('listPrice', finalPrice);
```

### 2. 页面调用链分析

#### 2.1 CCM_TrainingOrderView页面调用流程
1. **CCM_TrainingOrderView.cmp** - 前端页面组件
2. **CCM_TrainingOrderViewController.js** - 控制器调用`queryOrderInfo`方法
3. **CCM_TrainingOrderViewHelper.js** - Helper调用后端方法
4. **CCM_CourseRegisterCtl.queryOrderInfo** - 后端入口方法
5. **CCM_TrainingOrderInfo.queryOrderInfo** - 根据recordId类型分发
6. **CCM_CourseRegisterPreviewCtl.queryRegisterInfo** - 处理Course_Register__c类型记录

#### 2.2 价格显示位置
- **Request Item Information表格**: 显示每个Course Register Item的List Price
- **Training Amount汇总**: 基于Customer特定价格计算的培训总金额
- **VAT计算**: 基于Customer特定价格计算的税费
- **Total Due Amount**: 基于Customer特定价格计算的总金额

## 业务逻辑流程

### 价格显示优先级
1. **第一优先级**: Course Price Per Customer中的Customer特定价格
2. **第二优先级**: Course Arrangement的默认价格

### 页面显示逻辑
1. **Training Order View页面**:
   - 调用`queryOrderInfo`方法获取订单详情
   - 根据recordId类型（Training_Order__c或Course_Register__c）分发到不同处理方法
   - 对于Course_Register__c类型，调用`CCM_CourseRegisterPreviewCtl.queryRegisterInfo`
   - 在Course Register Item列表中显示Customer特定价格

2. **价格计算逻辑**:
   - 每个Course Register Item使用Customer特定价格（如果存在）
   - 汇总计算基于Customer特定价格
   - 保持原有的货币显示格式

## 技术实现细节

### 价格获取逻辑
```apex
// 统一的价格获取逻辑
Decimal customerPrice = CCM_CourseRegisterCtl.getCustomerSpecificPrice(courseArrangementId, customerId);
Decimal finalPrice = customerPrice != null ? customerPrice : defaultPrice;
```

### Customer ID获取
- 从Course_Register__c记录的Customer__c字段获取Customer ID
- 传递给价格获取方法进行Customer特定价格查询

### 错误处理
- 所有价格获取操作都包含try-catch错误处理
- 当获取Customer特定价格失败时，自动回退到默认价格
- 记录详细的调试信息用于问题排查

## 影响的页面组件

### CCM_TrainingOrderView组件
- **Request Item Information表格**: 显示Customer特定的List Price
- **价格汇总区域**: Training Amount、VAT、Total Due Amount基于Customer特定价格计算
- **货币显示**: 保持原有的多货币显示功能

### 前端显示
- 价格显示格式保持不变，使用`lightning:formattedNumber`组件
- 货币代码从Course Register的CurrencyIsoCode获取
- 支持多货币显示

## 测试覆盖

### 单元测试
- 新增`testCCMCourseRegisterPreviewCtlWithCustomerPrice`测试方法
- 测试Course Register Item中Customer特定价格的显示逻辑
- 验证价格回退机制的正确性

### 集成测试
- 测试完整的页面调用链中的价格显示逻辑
- 验证Customer特定价格在Training Order View页面中的显示效果

## 部署注意事项

1. **数据依赖**: 确保Course Price Per Customer记录已正确创建
2. **权限设置**: 确保用户有权限访问Course Price Per Customer对象
3. **测试验证**: 部署后验证不同Customer在Training Order View页面看到不同的价格
4. **回退机制**: 当Customer特定价格不存在时，确保显示默认价格

## 兼容性

- **向后兼容**: 当没有Course Price Per Customer记录时，显示原有的默认价格
- **多用户支持**: 不同Customer用户看到各自的特定价格
- **多货币支持**: 保持原有的多货币显示功能
- **页面性能**: 价格查询逻辑优化，不影响页面加载性能

## 相关页面

### 已修改的页面
1. **CCM_Community_Course_Detail** - Course详情页面价格显示
2. **CCM_TrainingOrderView** - Training Order查看页面价格显示

### 价格显示一致性
- 所有相关页面都使用相同的价格获取逻辑
- 确保Customer在不同页面看到一致的价格信息
- 统一的错误处理和回退机制

## 总结

这个修改确保了CCM_TrainingOrderView页面能够正确显示Customer特定的价格，提供了个性化的价格体验。通过修改CCM_CourseRegisterPreviewCtl.queryRegisterInfo方法，我们实现了：

1. **个性化价格显示**: 不同Customer看到各自的特定价格
2. **系统稳定性**: 完整的错误处理和回退机制
3. **向后兼容**: 不影响现有功能，平滑升级
4. **一致性**: 与其他页面的价格显示逻辑保持一致

用户现在可以在Training Order View页面看到为他们定制的个性化价格，提供了更好的用户体验和更灵活的定价策略。
