/* Filter section styling */
.slds-card {
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);
}

.slds-card__header-title {
    font-weight: 600;
    color: #080707;
}

/* Responsive design for filter controls */
@media (max-width: 768px) {
    .slds-col {
        width: 100% !important;
        margin-bottom: 1rem;
    }
}

/* Button styling */
lightning-button {
    margin-top: 0.5rem;
}

/* Combobox styling */
lightning-combobox {
    --slds-c-combobox-color-border: #d8dde6;
}
